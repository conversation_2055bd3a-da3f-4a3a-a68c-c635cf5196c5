import streamlit as st
import numpy as np

def generate_aoi_export_filename(base_name, extension):
    """
    Generate export filename with AOI parameters.
    
    Args:
        base_name (str): Base name for the file
        extension (str): File extension without the dot
        
    Returns:
        str: Formatted filename with AOI parameters
    """
    if (hasattr(st.session_state, 'aoi_inline_min') and 
        st.session_state.aoi_inline_min is not None):
        
        aoi_params = (f"_IL{st.session_state.aoi_inline_min}-{st.session_state.aoi_inline_max}"
                      f"_XL{st.session_state.aoi_xline_min}-{st.session_state.aoi_xline_max}")
        return f"{base_name}{aoi_params}.{extension}"
    else:
        return f"{base_name}.{extension}"

def sort_trace_indices_by_header_loader(header_loader, indices):
    """Return indices sorted by inline then crossline using SegyHeaderLoader.
    
    Args:
        header_loader: SegyHeaderLoader object containing inlines, crosslines, unique_indices
        indices: Array or list of trace indices to sort
        
    Returns:
        numpy.ndarray: Trace indices sorted by inline then crossline
    """
    indices = np.array(indices)
    if len(indices) == 0:
        return indices
        
    # Get inline and crossline values for the selected indices
    mask = np.isin(header_loader.unique_indices, indices)
    inl = header_loader.inlines[mask]
    xl = header_loader.crosslines[mask]
    
    # Sort by inline then crossline
    order = np.lexsort((xl, inl))
    return indices[order]


def sort_trace_indices(headers_df):
    """Sort trace indices first by inline then by crossline.

    Args:
        headers_df (pandas.DataFrame): DataFrame with at least the columns
            'inline', 'crossline', and 'trace_idx'. Each row represents a
            unique trace.

    Returns:
        list[int]: Trace indices sorted lexicographically (inline, crossline).
    """
    if headers_df.empty:
        return []

    # Ensure required columns exist
    missing_cols = {col for col in ("inline", "crossline", "trace_idx") if col not in headers_df.columns}
    if missing_cols:
        raise ValueError(f"headers_df is missing required columns: {missing_cols}")

    return (
        headers_df.sort_values(["inline", "crossline"], kind="mergesort")
        ["trace_idx"].to_list()
    )


def select_export_attributes(exportable_attrs, first_descriptor, sample_length):
    """
    Filters feasible attributes for export, matching logic from original implementation.
    Returns a list of exportable attribute names.
    
    Args:
        exportable_attrs: List of desired attribute names to export
        first_descriptor: Dictionary containing spectral descriptors for the first trace
        sample_length: Expected length of each attribute array
        
    Returns:
        list: List of attribute names that can be exported
    """
    # Filter exportable attributes
    valid_attrs = [
        key for key in exportable_attrs
        if key in first_descriptor and isinstance(first_descriptor[key], np.ndarray) and len(first_descriptor[key]) == sample_length
    ]
    
    # Optionally add WOSS if possible
    if (
        'hfc' in first_descriptor and
        'norm_fdom' in first_descriptor and
        'mag_voice_slope' in first_descriptor and
        'WOSS' not in valid_attrs and
        'WOSS' in exportable_attrs
    ):
        valid_attrs.append('WOSS')
    
    return valid_attrs
