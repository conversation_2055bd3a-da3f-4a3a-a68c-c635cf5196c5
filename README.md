# WOSS Seismic Analysis Tool

A comprehensive Streamlit-based application for seismic data analysis and visualization, featuring advanced spectral analysis, GPU acceleration, interactive Area of Interest (AOI) selection, and robust export capabilities.

## Features

- **Multi-format Data Loading**: Support for SEG-Y, LAS, and CSV files with comprehensive validation
- **Interactive Visualization**: Real-time plotting with customizable parameters and responsive layouts
- **Advanced AOI Selection**: Interactive polygon and polyline selection with spatial validation
- **GPU-Accelerated Analysis**: High-performance spectral decomposition with automatic CPU fallback
- **Robust Export System**: Batch processing with AOI-specific exports and comprehensive error handling
- **Quality Control**: Multi-layer data validation, error recovery, and progress tracking
- **Performance Optimization**: Memory-efficient processing with configurable batch sizes

## Main Pipeline Architecture

The application follows a **5-step workflow** managed by the main router (`app.py`):

1. **Load Data** - Import SEG-Y files and well marker data
2. **Configure Display** - Set up visualization parameters and processing options
3. **Select Area** - Choose analysis area (inline/crossline, polyline, AOI, or well markers)
4. **Analyze Data** - Perform GPU-accelerated spectral analysis
5. **Export Results** - View results and export processed data

## Directory Structure

```
1g_Modular_deep/
├── app.py                          # Main application router and entry point
├── README.md                       # This file
├── codebase_readme.md             # Detailed technical documentation
├── .gitignore                     # Git ignore rules
├── .streamlit/
│   └── config.toml                # Streamlit configuration
├── common/                        # Shared modules and utilities
│   ├── __init__.py
│   ├── constants.py               # Application constants and configurations
│   ├── session_state.py           # Session state management
│   ├── ui_elements.py             # Reusable UI components
│   └── validation_ui.py           # Data validation UI components
├── pages/                         # Workflow page modules
│   ├── __init__.py
│   ├── load_data_page.py          # Step 1: Data loading interface
│   ├── configure_display_page.py  # Step 2: Display configuration
│   ├── select_area_page.py        # Step 3: Area selection
│   ├── analyze_data_page.py       # Step 4: Analysis execution
│   └── export_results_page.py     # Step 5: Results and export
├── utils/                         # Processing and utility modules
│   ├── __init__.py
│   ├── data_utils.py              # Data loading and manipulation
│   ├── data_validation.py         # Data validation functions
│   ├── aoi_processing.py          # AOI-specific processing functions
│   ├── aoi_validation.py          # AOI validation and error handling
│   ├── dlogst_spec_descriptor_cpu.py # CPU-based spectral analysis
│   ├── dlogst_spec_descriptor_gpu.py # GPU-accelerated spectral analysis
│   ├── error_handling.py          # Centralized error handling
│   ├── export_utils.py            # Data export functionality
│   ├── general_utils.py           # General utility functions
│   ├── gpu_diagnostic.py          # GPU diagnostics and testing
│   ├── gpu_manager.py             # GPU availability management
│   ├── gpu_utils.py               # GPU utility functions
│   ├── precomputation_utils.py    # Pre-computation operations
│   ├── processing.py              # Core processing algorithms
│   ├── qc_utils.py                # Quality control utilities
│   ├── session_state_handler.py   # Advanced session state handling
│   └── visualization.py           # Data visualization functions
├── tests/                         # Test modules and demonstrations
│   ├── test_polyline_basic.py     # Basic polyline functionality tests
│   ├── test_aoi_functionality.py  # AOI functionality tests
│   ├── test_aoi_integration.py    # AOI integration tests
│   ├── test_aoi_validation.py     # AOI validation tests
│   ├── test_aoi_export_mapping.py # AOI export mapping tests
│   ├── test_export_utils.py       # Export utilities tests
│   ├── AOI_Enhancement_Demo.py    # AOI enhancement demonstration
│   ├── Advanced_AOI_Features_Demo.py # Advanced AOI features demo
│   └── run_phase2_tests.py        # Phase 2 test runner
├── docs/                          # Documentation
│   ├── AOI_Technical_Documentation.md # AOI technical documentation
│   └── AOI_User_Guide.md          # AOI user guide
├── backup_tk/                     # Backup implementations
│   ├── 3D_WOSS_Main_Script_init.py
│   └── dlogst_spec_descriptor_gpu_init.py
└── initial_app/                   # Reference implementation
    ├── app_ref.py                 # Reference application
    ├── data_utils.py              # Reference data utilities
    ├── processing.py              # Reference processing
    └── [other reference files]
```

## Quick Start

### Prerequisites

1. **GPU Requirements**:
   - CUDA-compatible GPU
   - CUDA drivers installed
   - CuPy library (`pip install cupy-cuda11x` or `cupy-cuda12x`)

2. **Python Dependencies**:
   - Python 3.8+
   - Streamlit
   - NumPy/SciPy
   - Pandas
   - Matplotlib/Plotly
   - segyio (for SEG-Y file handling)

### Installation

1. Clone or download the repository
2. Install required dependencies:
   ```bash
   pip install streamlit cupy-cuda11x numpy scipy pandas matplotlib plotly segyio
   ```
3. Run the application:
   ```bash
   streamlit run app.py
   ```

### Usage

1. **Load Data**: Upload SEG-Y files and optional well marker data
2. **Configure Display**: Set visualization parameters and processing options
3. **Select Area**: Choose your analysis area using one of four modes:
   - Inline/Crossline: Specific trace ranges
   - Polyline: Custom path analysis
   - AOI (Area of Interest): Polygon-based selection
   - Well Markers: Analysis around well locations
4. **Analyze Data**: Execute GPU-accelerated spectral analysis
5. **Export Results**: View and export your processed data

## Key Components

### Core Application (`app.py`)
- Manages the 5-step workflow navigation
- Handles GPU availability detection and lazy loading
- Provides sidebar navigation and state management
- Routes requests to appropriate page modules
- Implements prerequisite checks for each workflow step

### Common Modules (`common/`)
- **constants.py**: Application configuration and constants
- **session_state.py**: Centralized session state management
- **ui_elements.py**: Reusable UI components
- **validation_ui.py**: Data validation interfaces

### Page Modules (`pages/`)
Each page implements a specific workflow step with dedicated UI and logic.

### Utility Modules (`utils/`)
- **Data Processing**: `data_utils.py`, `processing.py`, spectral descriptor modules
- **AOI Support**: `aoi_processing.py`, `aoi_validation.py`
- **GPU Management**: `gpu_manager.py`, `gpu_utils.py`, `gpu_diagnostic.py`
- **Analysis Support**: `precomputation_utils.py`, `qc_utils.py`, `visualization.py`
- **Export and I/O**: `export_utils.py`, `general_utils.py`
- **Error Handling**: `error_handling.py`

## Spectral Analysis Capabilities

The tool computes multiple spectral descriptors:
- Spectral Slope
- Bandwidth
- Rolloff
- High Frequency Content (HFC)
- Magnitude*Voice Slope
- Spectral Decrease
- **WOSS (Weighted Optimum Spectral Shape)**

## Configuration

### Streamlit Configuration (`.streamlit/config.toml`)
```toml
[client]
showSidebarNavigation = false    # Custom navigation via app.py

[server]
maxUploadSize = 8000            # 8GB upload limit for large SEG-Y files
```

## Testing

The application includes comprehensive test suites:
- Unit tests for core functionality
- Integration tests for workflow components
- AOI-specific functionality tests
- Export and validation tests
- Demonstration scripts for advanced features

Run tests using:
```bash
python tests/run_phase2_tests.py
```

## Documentation

- `codebase_readme.md`: Detailed technical documentation
- `docs/AOI_Technical_Documentation.md`: AOI technical details
- `docs/AOI_User_Guide.md`: AOI user guide
- Various implementation and enhancement summaries

## Architecture Benefits

- **Scalability**: Modular design allows easy addition of new features
- **Maintainability**: Clear separation of UI, processing, and utility functions
- **Performance**: GPU acceleration for computationally intensive operations
- **User Experience**: Intuitive workflow with comprehensive validation and feedback
- **Flexibility**: Multiple analysis modes to suit different geological scenarios

## Recent Enhancements

- Enhanced AOI (Area of Interest) functionality with polygon selection
- Improved error handling and validation
- Advanced export capabilities
- GPU diagnostics and performance optimization
- Comprehensive test coverage

## Support

For technical issues or questions, refer to the documentation in the `docs/` directory or examine the test files for usage examples.

## License

[Add your license information here]